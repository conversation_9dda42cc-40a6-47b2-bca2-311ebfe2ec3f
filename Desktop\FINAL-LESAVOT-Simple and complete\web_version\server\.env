# LESAVOT API Server Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1

# PostgreSQL Configuration (Render Database)
DATABASE_URL=postgresql://bechi:<EMAIL>/lesavotdb
DB_HOST=dpg-d1f1sgje5dus73felvp0-a.oregon-postgres.render.com
DB_PORT=5432
DB_NAME=lesavotdb
DB_USER=bechi
DB_PASSWORD=AmUZSSaMTbygPxpv9t3wN70y9xf6KNWH
DB_SSL=true

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (for OTP) - Using a test email service for development
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password-here
EMAIL_FROM=LESAVOT Security <<EMAIL>>

# Security Configuration
ENABLE_HELMET=true
ENABLE_CONTENT_SECURITY_POLICY=false
ENABLE_XSS_PROTECTION=true
SHOW_STACK_TRACES=true
LOG_ERRORS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080,http://127.0.0.1:8080,https://lasavot.onrender.com
ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# Logging Configuration
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,audio/wav,audio/mp3,text/plain

# Session Configuration
SESSION_SECRET=your-session-secret-change-this-in-production
SESSION_TIMEOUT=1800000

# OTP Configuration
OTP_EXPIRY_SECONDS=300
OTP_LENGTH=6
