/**
 * User Model for MongoDB
 * 
 * Defines the user schema and provides user-related database operations
 */

const { ObjectId } = require('mongodb');
const bcrypt = require('bcrypt');
const database = require('../utils/database');
const logger = require('../utils/logger');

class User {
  constructor(userData) {
    this.username = userData.username;
    this.email = userData.email;
    this.password = userData.password;
    this.firstName = userData.firstName || '';
    this.lastName = userData.lastName || '';
    this.role = userData.role || 'user';
    this.isActive = userData.isActive !== undefined ? userData.isActive : true;
    this.emailVerified = userData.emailVerified || false;
    this.lastLogin = userData.lastLogin || null;
    this.createdAt = userData.createdAt || new Date();
    this.updatedAt = userData.updatedAt || new Date();
    this.preferences = userData.preferences || {
      theme: 'light',
      language: 'en',
      notifications: true
    };
    this.securitySettings = userData.securitySettings || {
      twoFactorEnabled: false,
      loginAttempts: 0,
      lockedUntil: null
    };
  }

  /**
   * Hash password before saving
   */
  async hashPassword() {
    if (this.password) {
      const saltRounds = 12;
      this.password = await bcrypt.hash(this.password, saltRounds);
    }
  }

  /**
   * Verify password
   */
  async verifyPassword(plainPassword) {
    return await bcrypt.compare(plainPassword, this.password);
  }

  /**
   * Save user to database
   */
  async save() {
    try {
      const collection = database.getCollection('users');
      
      // Hash password if it's a new user or password is being updated
      if (this.password && !this.password.startsWith('$2b$')) {
        await this.hashPassword();
      }
      
      this.updatedAt = new Date();
      
      if (this._id) {
        // Update existing user
        const result = await collection.updateOne(
          { _id: new ObjectId(this._id) },
          { $set: this.toObject() }
        );
        return result.modifiedCount > 0;
      } else {
        // Create new user
        const result = await collection.insertOne(this.toObject());
        this._id = result.insertedId;
        return true;
      }
    } catch (error) {
      logger.error('Error saving user:', error);
      throw error;
    }
  }

  /**
   * Find user by username
   */
  static async findByUsername(username) {
    try {
      const collection = database.getCollection('users');
      const userData = await collection.findOne({ username });
      return userData ? new User(userData) : null;
    } catch (error) {
      logger.error('Error finding user by username:', error);
      throw error;
    }
  }

  /**
   * Find user by email
   */
  static async findByEmail(email) {
    try {
      const collection = database.getCollection('users');
      const userData = await collection.findOne({ email });
      return userData ? new User(userData) : null;
    } catch (error) {
      logger.error('Error finding user by email:', error);
      throw error;
    }
  }

  /**
   * Find user by ID
   */
  static async findById(id) {
    try {
      const collection = database.getCollection('users');
      const userData = await collection.findOne({ _id: new ObjectId(id) });
      return userData ? new User(userData) : null;
    } catch (error) {
      logger.error('Error finding user by ID:', error);
      throw error;
    }
  }

  /**
   * Update last login time
   */
  async updateLastLogin() {
    try {
      const collection = database.getCollection('users');
      this.lastLogin = new Date();
      await collection.updateOne(
        { _id: new ObjectId(this._id) },
        { $set: { lastLogin: this.lastLogin } }
      );
    } catch (error) {
      logger.error('Error updating last login:', error);
      throw error;
    }
  }

  /**
   * Increment login attempts
   */
  async incrementLoginAttempts() {
    try {
      const collection = database.getCollection('users');
      this.securitySettings.loginAttempts += 1;
      
      // Lock account after 5 failed attempts for 30 minutes
      if (this.securitySettings.loginAttempts >= 5) {
        this.securitySettings.lockedUntil = new Date(Date.now() + 30 * 60 * 1000);
      }
      
      await collection.updateOne(
        { _id: new ObjectId(this._id) },
        { $set: { securitySettings: this.securitySettings } }
      );
    } catch (error) {
      logger.error('Error incrementing login attempts:', error);
      throw error;
    }
  }

  /**
   * Reset login attempts
   */
  async resetLoginAttempts() {
    try {
      const collection = database.getCollection('users');
      this.securitySettings.loginAttempts = 0;
      this.securitySettings.lockedUntil = null;
      
      await collection.updateOne(
        { _id: new ObjectId(this._id) },
        { $set: { securitySettings: this.securitySettings } }
      );
    } catch (error) {
      logger.error('Error resetting login attempts:', error);
      throw error;
    }
  }

  /**
   * Check if account is locked
   */
  isAccountLocked() {
    return this.securitySettings.lockedUntil && 
           this.securitySettings.lockedUntil > new Date();
  }

  /**
   * Convert to plain object (for database storage)
   */
  toObject() {
    const obj = { ...this };
    if (obj._id && typeof obj._id === 'string') {
      obj._id = new ObjectId(obj._id);
    }
    return obj;
  }

  /**
   * Convert to safe object (for API responses - removes sensitive data)
   */
  toSafeObject() {
    const obj = { ...this };
    delete obj.password;
    delete obj.securitySettings;
    return obj;
  }

  /**
   * Get all users (admin only)
   */
  static async findAll(limit = 50, skip = 0) {
    try {
      const collection = database.getCollection('users');
      const users = await collection
        .find({})
        .limit(limit)
        .skip(skip)
        .sort({ createdAt: -1 })
        .toArray();
      
      return users.map(userData => new User(userData));
    } catch (error) {
      logger.error('Error finding all users:', error);
      throw error;
    }
  }

  /**
   * Delete user
   */
  async delete() {
    try {
      const collection = database.getCollection('users');
      const result = await collection.deleteOne({ _id: new ObjectId(this._id) });
      return result.deletedCount > 0;
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw error;
    }
  }
}

module.exports = User;
