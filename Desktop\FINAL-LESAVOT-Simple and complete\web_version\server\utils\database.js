/**
 * MongoDB Database Connection and Utilities
 * 
 * This module handles MongoDB connection, collection management,
 * and provides database utilities for the LESAVOT application.
 */

const { MongoClient, ServerApi } = require('mongodb');
const mongoose = require('mongoose');
const logger = require('./logger');

class Database {
  constructor() {
    this.client = null;
    this.db = null;
    this.isConnected = false;
    this.connectionString = process.env.MONGODB_URI || "mongodb+srv://seclesavot:<EMAIL>/?retryWrites=true&w=majority&appName=ASH";
    this.dbName = process.env.MONGODB_DB_NAME || 'lesavot_db';
  }

  /**
   * Connect to MongoDB
   */
  async connect() {
    try {
      if (this.isConnected) {
        logger.info('Database already connected');
        return this.db;
      }

      logger.info('Connecting to MongoDB...');
      
      // Create MongoDB client with proper configuration
      this.client = new MongoClient(this.connectionString, {
        serverApi: {
          version: ServerApi.v1,
          strict: true,
          deprecationErrors: true,
        },
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferMaxEntries: 0,
        useNewUrlParser: true,
        useUnifiedTopology: true
      });

      // Connect to MongoDB
      await this.client.connect();
      
      // Test the connection
      await this.client.db("admin").command({ ping: 1 });
      
      // Get database instance
      this.db = this.client.db(this.dbName);
      this.isConnected = true;

      logger.info(`Successfully connected to MongoDB database: ${this.dbName}`);
      
      // Initialize collections and indexes
      await this.initializeCollections();
      
      return this.db;
    } catch (error) {
      logger.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Initialize collections and create indexes
   */
  async initializeCollections() {
    try {
      logger.info('Initializing MongoDB collections...');

      // Users collection
      const usersCollection = this.db.collection('users');
      await usersCollection.createIndex({ username: 1 }, { unique: true });
      await usersCollection.createIndex({ email: 1 }, { unique: true });
      await usersCollection.createIndex({ createdAt: 1 });

      // Sessions collection (for OTP and authentication)
      const sessionsCollection = this.db.collection('sessions');
      await sessionsCollection.createIndex({ username: 1 });
      await sessionsCollection.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });
      await sessionsCollection.createIndex({ sessionId: 1 }, { unique: true });

      // Steganography operations collection
      const stegoCollection = this.db.collection('steganography_operations');
      await stegoCollection.createIndex({ userId: 1 });
      await stegoCollection.createIndex({ createdAt: 1 });
      await stegoCollection.createIndex({ operationType: 1 });
      await stegoCollection.createIndex({ status: 1 });

      // History collection
      const historyCollection = this.db.collection('history');
      await historyCollection.createIndex({ userId: 1 });
      await historyCollection.createIndex({ createdAt: 1 });
      await historyCollection.createIndex({ operationType: 1 });

      // Metrics collection
      const metricsCollection = this.db.collection('metrics');
      await metricsCollection.createIndex({ timestamp: 1 });
      await metricsCollection.createIndex({ userId: 1 });
      await metricsCollection.createIndex({ metricType: 1 });

      // Files collection (for storing file metadata)
      const filesCollection = this.db.collection('files');
      await filesCollection.createIndex({ userId: 1 });
      await filesCollection.createIndex({ fileHash: 1 });
      await filesCollection.createIndex({ createdAt: 1 });

      logger.info('MongoDB collections and indexes initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize collections:', error);
      throw error;
    }
  }

  /**
   * Get a collection
   */
  getCollection(name) {
    if (!this.isConnected || !this.db) {
      throw new Error('Database not connected');
    }
    return this.db.collection(name);
  }

  /**
   * Close database connection
   */
  async close() {
    try {
      if (this.client && this.isConnected) {
        await this.client.close();
        this.isConnected = false;
        this.client = null;
        this.db = null;
        logger.info('MongoDB connection closed');
      }
    } catch (error) {
      logger.error('Error closing MongoDB connection:', error);
      throw error;
    }
  }

  /**
   * Check if database is connected
   */
  isConnectedToDatabase() {
    return this.isConnected && this.client && this.db;
  }

  /**
   * Get database statistics
   */
  async getStats() {
    try {
      if (!this.isConnectedToDatabase()) {
        throw new Error('Database not connected');
      }

      const stats = await this.db.stats();
      return {
        database: this.dbName,
        collections: stats.collections,
        dataSize: stats.dataSize,
        storageSize: stats.storageSize,
        indexes: stats.indexes,
        indexSize: stats.indexSize
      };
    } catch (error) {
      logger.error('Error getting database stats:', error);
      throw error;
    }
  }
}

// Create singleton instance
const database = new Database();

module.exports = database;
